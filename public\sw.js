// DERS PWA Service Worker
// Version: 1.0.0

const CACHE_NAME = 'ders-pwa-v1';
const urlsToCache = [
  '/ders/',
  '/ders/public/assets/css/bootstrap.min.css',
  '/ders/public/assets/js/bootstrap.bundle.min.js',
  '/ders/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css',
  '/ders/public/assets/system_img/pwa-icons/icon-192x192.png',
  '/ders/public/assets/system_img/pwa-icons/icon-512x512.png'
];

// Install event - cache resources
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
      .catch(function(error) {
        // Silent error handling - no console logs
      })
  );
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', function(event) {
  // Skip caching for external resources and API calls
  if (event.request.url.includes('generativelanguage.googleapis.com') ||
      event.request.url.includes('cdnjs.cloudflare.com') ||
      event.request.url.includes('api.') ||
      event.request.method !== 'GET') {
    
    // Fetch from network without caching - no console logs
    event.respondWith(fetch(event.request));
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Return cached version or fetch from network
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch(function(error) {
        // Silent error handling - no console logs
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Handle skip waiting message
self.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
