<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>">Pre-Screening Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pre-Screening Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Two Column Layout -->
<div class="row">
    <!-- Left Column - Applicant Profile -->
    <div class="col-lg-8">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Applicant Profile: <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active text-dark" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fas fa-user-circle"></i> Applicant Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link text-dark" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false">
                            <i class="fas fa-file-pdf"></i> Files (<?= count($applicant_files) ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="profileTabsContent">
                    <!-- Applicant Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <?php $firstApp = $applications[0]; ?>

                                <!-- Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Personal Information</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Full Name:</strong> <?= esc($firstApp['first_name'] . ' ' . $firstApp['last_name']) ?></p>
                                                <p><strong>Gender:</strong> <?= esc($firstApp['gender']) ?></p>
                                                <p><strong>Date of Birth:</strong> <?= esc($firstApp['date_of_birth']) ?></p>
                                                <p><strong>Place of Origin:</strong> <?= esc($firstApp['place_of_origin']) ?></p>
                                                <p><strong>Citizenship:</strong> <?= esc($firstApp['citizenship']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Email:</strong> <?= esc($firstApp['email_address']) ?></p>
                                                <p><strong>Contact Details:</strong> <?= esc($firstApp['contact_details']) ?></p>
                                                <p><strong>Address:</strong> <?= esc($firstApp['location_address']) ?></p>
                                                <p><strong>ID Numbers:</strong> <?= esc($firstApp['id_numbers']) ?></p>
                                                <p><strong>Marital Status:</strong> <?= esc($firstApp['marital_status']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Employment Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Current Employment</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Current Employer:</strong> <?= esc($firstApp['current_employer']) ?></p>
                                                <p><strong>Current Position:</strong> <?= esc($firstApp['current_position']) ?></p>
                                                <p><strong>Current Salary:</strong> <?= esc($firstApp['current_salary']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Public Servant:</strong>
                                                    <span class="badge bg-<?= $firstApp['is_public_servant'] ? 'success' : 'secondary' ?>">
                                                        <?= $firstApp['is_public_servant'] ? 'Yes' : 'No' ?>
                                                    </span>
                                                </p>
                                                <?php if ($firstApp['is_public_servant']): ?>
                                                    <p><strong>File Number:</strong> <?= esc($firstApp['public_service_file_number']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Additional Information</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Children:</strong> <?= esc($firstApp['children']) ?></p>
                                                <p><strong>Offence Convicted:</strong> <?= esc($firstApp['offence_convicted']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>How did you hear about us:</strong> <?= esc($firstApp['how_did_you_hear_about_us']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Referees -->
                                <?php if (!empty($firstApp['referees'])): ?>
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Referees</h6>
                                        <div class="bg-light p-3 rounded">
                                            <?= nl2br(esc($firstApp['referees'])) ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Publications & Awards -->
                                <div class="row">
                                    <?php if (!empty($firstApp['publications'])): ?>
                                    <div class="col-md-6">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Publications</h6>
                                        <div class="bg-light p-3 rounded">
                                            <?= nl2br(esc($firstApp['publications'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($firstApp['awards'])): ?>
                                    <div class="col-md-6">
                                        <h6 class="text-red border-bottom pb-2 mb-3">Awards</h6>
                                        <div class="bg-light p-3 rounded">
                                            <?= nl2br(esc($firstApp['awards'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> No application details found.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Files Tab -->
                    <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                        <div class="p-4">
                            <?php if (!empty($applicant_files)): ?>
                                <div class="mb-3">
                                    <h6 class="text-red">PDF Files Viewer</h6>
                                    <p class="text-muted">All PDF files are merged and displayed below. Scroll through to view all documents.</p>
                                </div>

                                <!-- PDF Viewer Container -->
                                <div id="pdfViewerContainer" style="height: 800px; border: 1px solid #ddd; border-radius: 8px; overflow-y: auto;">
                                    <div id="pdfViewer" class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading PDFs...</span>
                                        </div>
                                        <p class="mt-2">Loading PDF files...</p>
                                    </div>
                                </div>

                                <!-- Files List -->
                                <div class="mt-4">
                                    <h6 class="text-red">Files List</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th class="text-white">#</th>
                                                    <th class="text-white">File Title</th>
                                                    <th class="text-white">Description</th>
                                                    <th class="text-white">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($applicant_files as $index => $file): ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td>
                                                        <i class="fas fa-file-pdf text-danger"></i>
                                                        <?= esc($file['file_title']) ?>
                                                    </td>
                                                    <td><?= esc($file['file_description']) ?></td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-external-link-alt"></i> Open
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="analyzeFile(<?= $file['id'] ?>, '<?= esc($file['file_title']) ?>', '<?= esc($file['file_path']) ?>')">
                                                                <i class="fas fa-brain"></i> Analyse
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No files uploaded for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column - Pre-Screening Criteria -->
    <div class="col-lg-4">
        <div class="card hover-card">
            <div class="card-header bg-yellow text-black">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check"></i> Pre-Screening Criteria
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($pre_screen_criteria)): ?>
                    <div class="criteria-list">
                        <?php foreach ($pre_screen_criteria as $index => $criteria): ?>
                            <div class="criteria-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="text-red mb-0">
                                        <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                        <?= esc($criteria['title'] ?? 'Criteria ' . ($index + 1)) ?>
                                    </h6>
                                    <span class="badge bg-<?= ($criteria['weight'] ?? 0) >= 70 ? 'danger' : (($criteria['weight'] ?? 0) >= 40 ? 'warning' : 'secondary') ?>">
                                        <?= esc($criteria['weight'] ?? 0) ?>%
                                    </span>
                                </div>
                                <p class="text-muted mb-2"><?= esc($criteria['description'] ?? '') ?></p>

                                <?php if (!empty($criteria['requirements'])): ?>
                                    <div class="requirements">
                                        <small class="text-muted d-block mb-1"><strong>Requirements:</strong></small>
                                        <ul class="list-unstyled ms-3">
                                            <?php foreach ($criteria['requirements'] as $requirement): ?>
                                                <li class="mb-1">
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    <small><?= esc($requirement) ?></small>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div class="criteria-actions mt-2">
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <button type="button" class="btn btn-outline-success" onclick="markCriteria(<?= $index ?>, 'pass')">
                                            <i class="fas fa-check"></i> Pass
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="markCriteria(<?= $index ?>, 'fail')">
                                            <i class="fas fa-times"></i> Fail
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Overall Assessment -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="text-red">Overall Assessment</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-success" id="passedCount">0</div>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-danger" id="failedCount">0</div>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-warning" id="pendingCount"><?= count($pre_screen_criteria) ?></div>
                                    <small class="text-muted">Pending</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <small class="text-muted">Completion Progress</small>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No pre-screening criteria defined for this exercise.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<style>
/* Fix tab text colors for better visibility */
.nav-tabs .nav-link {
    color: #333 !important;
}

.nav-tabs .nav-link.active {
    color: #000 !important;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    color: #F00F00 !important;
}
</style>

<script>
// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// Criteria tracking
let criteriaResults = {};
const totalCriteria = <?= count($pre_screen_criteria) ?>;

$(document).ready(function() {
    // Load PDFs immediately on page load
    loadAllPDFs();

    // Initialize criteria tracking
    updateCriteriaSummary();
});

// Load and convert all PDF files to images on page load
async function loadAllPDFs() {
    const pdfViewer = document.getElementById('pdfViewer');
    const files = <?= json_encode($applicant_files) ?>;

    if (!pdfViewer) {
        console.error('PDF viewer element not found');
        return;
    }

    if (files.length === 0) {
        pdfViewer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> No PDF files to display.</div>';
        return;
    }

    // Show loading state
    pdfViewer.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <p class="mt-2">Converting PDF files to images...</p>
            <p class="text-muted">Processing ${files.length} file(s)...</p>
        </div>
    `;

    try {
        let allImagesHtml = '';
        let totalPages = 0;
        let processedPages = 0;

        // First, count total pages for progress tracking
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;
                totalPages += pdf.numPages;
            } catch (error) {
                console.error('Error counting pages for:', file.file_title, error);
            }
        }

        // Process each file and convert pages to images
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            // Add file header
            allImagesHtml += `
                <div class="pdf-file-header bg-red text-white p-2 mb-3 rounded">
                    <h6 class="mb-0">
                        <i class="fas fa-file-pdf"></i> ${file.file_title || 'Unknown File'}
                        ${file.file_description ? `<small class="ms-2">(${file.file_description})</small>` : ''}
                    </h6>
                </div>
            `;

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;

                // Convert each page to image
                for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    // Update progress
                    processedPages++;
                    const progress = Math.round((processedPages / totalPages) * 100);

                    pdfViewer.innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <p class="mt-2">Converting PDF pages to images...</p>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar bg-red" role="progressbar" style="width: ${progress}%" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                                    ${progress}%
                                </div>
                            </div>
                            <p class="text-muted">Processing page ${processedPages} of ${totalPages}</p>
                        </div>
                    `;

                    const page = await pdf.getPage(pageNum);
                    const viewport = page.getViewport({ scale: 1.5 }); // Higher scale for better quality

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert canvas to high-quality image
                    const imageDataUrl = canvas.toDataURL('image/png', 1.0);

                    allImagesHtml += `
                        <div class="pdf-page-image mb-4 text-center" data-file-index="${i}" data-page="${pageNum}">
                            <div class="page-info bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <strong>${file.file_title}</strong> - Page ${pageNum} of ${pdf.numPages}
                                </small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'in')" title="Zoom In">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'out')" title="Zoom Out">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'reset')" title="Reset Zoom">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="image-container" style="overflow: auto; max-height: 800px;">
                                <img src="${imageDataUrl}"
                                     class="pdf-page-img img-fluid border rounded shadow-sm"
                                     style="max-width: 100%; cursor: zoom-in;"
                                     onclick="toggleFullscreen(this)"
                                     alt="Page ${pageNum} of ${file.file_title}" />
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error converting PDF to images:', file.file_title, error);
                allImagesHtml += `
                    <div class="alert alert-warning mb-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        Could not convert PDF to images: ${file.file_title || 'Unknown File'}
                        <br><small>Error: ${error.message}</small>
                    </div>
                `;
            }
        }

        // Display all converted images
        if (pdfViewer) {
            pdfViewer.innerHTML = allImagesHtml;

            // Add completion message
            const completionMessage = `
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle"></i>
                    Successfully converted ${totalPages} pages from ${files.length} PDF file(s) to images.
                </div>
            `;
            pdfViewer.insertAdjacentHTML('afterbegin', completionMessage);
        }

    } catch (error) {
        console.error('Error converting PDFs to images:', error);
        if (pdfViewer) {
            pdfViewer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    Error converting PDF files to images: ${error.message}
                </div>
            `;
        }
    }
}

// Mark criteria as pass/fail
function markCriteria(index, result) {
    criteriaResults[index] = result;

    // Update button states
    const criteriaItem = document.querySelectorAll('.criteria-item')[index];
    const buttons = criteriaItem.querySelectorAll('.btn-group button');

    buttons.forEach(btn => btn.classList.remove('active'));

    if (result === 'pass') {
        buttons[0].classList.add('active', 'btn-success');
        buttons[0].classList.remove('btn-outline-success');
        buttons[1].classList.add('btn-outline-danger');
        buttons[1].classList.remove('btn-danger');
    } else {
        buttons[1].classList.add('active', 'btn-danger');
        buttons[1].classList.remove('btn-outline-danger');
        buttons[0].classList.add('btn-outline-success');
        buttons[0].classList.remove('btn-success');
    }

    updateCriteriaSummary();
}

// Update criteria summary
function updateCriteriaSummary() {
    const passed = Object.values(criteriaResults).filter(r => r === 'pass').length;
    const failed = Object.values(criteriaResults).filter(r => r === 'fail').length;
    const pending = totalCriteria - passed - failed;

    const passedElement = document.getElementById('passedCount');
    const failedElement = document.getElementById('failedCount');
    const pendingElement = document.getElementById('pendingCount');
    const progressElement = document.getElementById('progressBar');

    if (passedElement) passedElement.textContent = passed;
    if (failedElement) failedElement.textContent = failed;
    if (pendingElement) pendingElement.textContent = pending;

    const progress = totalCriteria > 0 ? ((passed + failed) / totalCriteria) * 100 : 0;
    if (progressElement) progressElement.style.width = progress + '%';
}

// Image zoom functionality
function zoomImage(button, action) {
    const imageContainer = button.closest('.pdf-page-image').querySelector('.image-container');
    const image = imageContainer.querySelector('.pdf-page-img');

    let currentScale = parseFloat(image.dataset.scale || '1');

    switch(action) {
        case 'in':
            currentScale = Math.min(currentScale * 1.2, 3); // Max 3x zoom
            break;
        case 'out':
            currentScale = Math.max(currentScale / 1.2, 0.5); // Min 0.5x zoom
            break;
        case 'reset':
            currentScale = 1;
            break;
    }

    image.dataset.scale = currentScale;
    image.style.transform = `scale(${currentScale})`;
    image.style.transformOrigin = 'center';

    // Update cursor based on zoom level
    if (currentScale > 1) {
        image.style.cursor = 'zoom-out';
    } else {
        image.style.cursor = 'zoom-in';
    }
}

// Toggle fullscreen for images
function toggleFullscreen(image) {
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        const container = image.closest('.pdf-page-image');
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }
    }
}

// AI File Analysis Function
async function analyzeFile(fileId, fileName, filePath) {
    // Show loading modal
    Swal.fire({
        title: 'Analyzing File with AI',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p>AI is analyzing: <strong>${fileName}</strong></p>
                <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                <p class="text-muted">Reading text, images, tables, and charts...</p>
            </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        // Convert PDF to base64 for multimodal analysis
        const fullFilePath = '<?= base_url() ?>' + filePath;
        const pdfBase64 = await convertPDFToBase64(fullFilePath);

        if (!pdfBase64) {
            throw new Error('Could not convert PDF file for AI analysis');
        }

        // Send to AI for multimodal analysis
        const analysisResult = await sendToGeminiMultimodal(pdfBase64, fileName);

        // Show results
        Swal.fire({
            title: 'AI Analysis Complete',
            html: `
                <div class="text-start">
                    <h6 class="text-primary mb-3">File: ${fileName}</h6>
                    <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                        <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${JSON.stringify(analysisResult, null, 2)}</pre>
                    </div>
                </div>
            `,
            width: '80%',
            confirmButtonText: 'Close',
            confirmButtonColor: '#F00F00'
        });

    } catch (error) {
        console.error('Error analyzing file:', error);
        Swal.fire({
            title: 'Analysis Failed',
            text: `Error analyzing file: ${error.message}`,
            icon: 'error',
            confirmButtonColor: '#F00F00'
        });
    }
}

// Convert PDF to base64 for multimodal analysis
async function convertPDFToBase64(pdfUrl) {
    try {
        const response = await fetch(pdfUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        // Convert to base64
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }

        return btoa(binary);
    } catch (error) {
        console.error('Error converting PDF to base64:', error);
        throw new Error('Failed to convert PDF to base64: ' + error.message);
    }
}

// Send PDF to Gemini Flash 2.5 for multimodal analysis
async function sendToGeminiMultimodal(pdfBase64, fileName) {
    const prompt = `
Please analyze this PDF document comprehensively using your multimodal capabilities. Read and extract information from:
- All text content
- Images, photos, and graphics
- Tables and charts
- Diagrams and visual elements
- Layout and formatting

DOCUMENT: ${fileName}

Provide a structured JSON response with the applicant's complete profile information:

{
    "document_type": "CV/Resume/Cover Letter/Certificate/Transcript/Portfolio/Other",
    "document_analysis": {
        "total_pages": 0,
        "has_images": false,
        "has_tables": false,
        "has_charts": false,
        "layout_quality": "professional/basic/poor"
    },
    "personal_information": {
        "name": "",
        "email": "",
        "phone": "",
        "address": "",
        "date_of_birth": "",
        "nationality": "",
        "linkedin_profile": "",
        "photo_present": false
    },
    "education": [
        {
            "institution": "",
            "degree": "",
            "field_of_study": "",
            "graduation_year": "",
            "grade": "",
            "honors": "",
            "relevant_coursework": []
        }
    ],
    "work_experience": [
        {
            "company": "",
            "position": "",
            "start_date": "",
            "end_date": "",
            "duration": "",
            "responsibilities": [],
            "achievements": [],
            "technologies_used": []
        }
    ],
    "skills": {
        "technical_skills": [],
        "soft_skills": [],
        "languages": [],
        "software_proficiency": [],
        "programming_languages": []
    },
    "certifications": [
        {
            "name": "",
            "issuing_organization": "",
            "date_obtained": "",
            "expiry_date": "",
            "credential_id": ""
        }
    ],
    "projects": [
        {
            "name": "",
            "description": "",
            "technologies": [],
            "duration": "",
            "role": ""
        }
    ],
    "achievements_awards": [],
    "publications": [],
    "references": [
        {
            "name": "",
            "position": "",
            "company": "",
            "contact": "",
            "relationship": ""
        }
    ],
    "visual_elements": {
        "charts_data": [],
        "images_description": [],
        "tables_content": []
    },
    "summary": "Comprehensive summary of the candidate's profile",
    "key_strengths": [],
    "areas_of_expertise": [],
    "career_progression": "",
    "overall_assessment": {
        "experience_level": "entry/mid/senior/executive",
        "profile_completeness": "excellent/good/fair/poor",
        "presentation_quality": "excellent/good/fair/poor"
    }
}

Extract ALL information present in the document, including data from images, tables, charts, and visual elements. If a field is not available, use null or an empty array.
`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [
                    {
                        text: prompt
                    },
                    {
                        inline_data: {
                            mime_type: "application/pdf",
                            data: pdfBase64
                        }
                    }
                ]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.95,
                maxOutputTokens: 4096,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}
</script>
<?= $this->endSection() ?>
