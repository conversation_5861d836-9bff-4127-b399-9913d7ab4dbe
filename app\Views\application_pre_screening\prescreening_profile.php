<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>">Pre-Screening Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pre-Screening Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Two Column Layout -->
<div class="row">
    <!-- Left Column - Applicant Profile -->
    <div class="col-lg-8">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Applicant Profile: <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active text-dark" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fas fa-user-circle"></i> Applicant Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link text-dark" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false">
                            <i class="fas fa-file-pdf"></i> Files (<?= count($applicant_files) ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="profileTabsContent">
                    <!-- Applicant Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <?php $firstApp = $applications[0]; ?>

                                <!-- 1. Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-user me-2"></i>Personal Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Basic details and contact info</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Full Name:</strong> <?= esc($firstApp['first_name'] . ' ' . $firstApp['last_name']) ?></p>
                                                <p><strong>Gender:</strong> <?= esc($firstApp['gender']) ?></p>
                                                <p><strong>Date of Birth:</strong>
                                                    <span id="dobDisplay"><?= esc($firstApp['date_of_birth']) ?></span>
                                                    <span id="ageDisplay" class="badge bg-secondary ms-2"></span>
                                                </p>
                                                <p><strong>Place of Origin:</strong> <?= esc($firstApp['place_of_origin']) ?></p>
                                                <p><strong>Citizenship:</strong> <?= esc($firstApp['citizenship']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Email:</strong> <?= esc($firstApp['email_address']) ?></p>
                                                <p><strong>Contact Details:</strong> <?= esc($firstApp['contact_details']) ?></p>
                                                <p><strong>Address:</strong> <?= esc($firstApp['location_address']) ?></p>
                                                <p><strong>Marital Status:</strong> <?= esc($firstApp['marital_status']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 2. Documents & ID -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-id-card me-2"></i>Documents & ID
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Identification and records</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>ID Numbers:</strong> <?= esc($firstApp['id_numbers']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Offence Convicted:</strong> <?= esc($firstApp['offence_convicted']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 3. Employment -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-briefcase me-2"></i>Employment
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Current work information</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Current Employer:</strong> <?= esc($firstApp['current_employer']) ?></p>
                                                <p><strong>Current Position:</strong> <?= esc($firstApp['current_position']) ?></p>
                                                <p><strong>Current Salary:</strong> <?= esc($firstApp['current_salary']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Public Servant:</strong>
                                                    <span class="badge bg-<?= $firstApp['is_public_servant'] ? 'success' : 'secondary' ?>">
                                                        <?= $firstApp['is_public_servant'] ? 'Yes' : 'No' ?>
                                                    </span>
                                                </p>
                                                <?php if ($firstApp['is_public_servant']): ?>
                                                    <p><strong>File Number:</strong> <?= esc($firstApp['public_service_file_number']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 4. Work Experience -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-history me-2"></i>Work Experience
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Previous employment history</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Work experience details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 5. Education -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-graduation-cap me-2"></i>Education
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Academic qualifications</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Education details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 6. Family Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-users me-2"></i>Family Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Family details and dependents</small>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <strong>Children:</strong>
                                                    <div id="childrenDisplay" class="mt-2">
                                                        <!-- Children will be displayed here in table format -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Referees -->
                                        <?php if (!empty($firstApp['referees'])): ?>
                                        <div class="mt-4">
                                            <strong>Referees:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= nl2br(esc($firstApp['referees'])) ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- 7. Achievements -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-star me-2"></i>Achievements
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Awards and accomplishments</small>
                                        <div class="row">
                                            <?php if (!empty($firstApp['publications'])): ?>
                                            <div class="col-md-6">
                                                <strong>Publications:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['publications'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (!empty($firstApp['awards'])): ?>
                                            <div class="col-md-6">
                                                <strong>Awards:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['awards'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (empty($firstApp['publications']) && empty($firstApp['awards'])): ?>
                                            <div class="col-12">
                                                <div class="bg-light p-3 rounded">
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        No achievements or awards information provided.
                                                    </p>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Additional Information -->
                                        <div class="mt-4">
                                            <strong>How did you hear about us:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= esc($firstApp['how_did_you_hear_about_us']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> No application details found.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Files Tab -->
                    <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                        <div class="p-4">
                            <?php if (!empty($applicant_files)): ?>
                                <div class="mb-3">
                                    <h6 class="text-red">PDF Files Viewer</h6>
                                    <p class="text-muted">All PDF files are merged and displayed below. Scroll through to view all documents.</p>
                                </div>

                                <!-- AI Analysis Button -->
                                <div class="mb-4">
                                    <button type="button" class="btn btn-success btn-lg" id="analyzeFilesBtn" onclick="analyzeAllFilesWithAI()">
                                        <i class="fas fa-brain me-2"></i>Analyse Files with AI
                                    </button>
                                    <p class="text-muted mt-2 mb-0">
                                        <i class="fas fa-info-circle me-1"></i>
                                        AI will analyze all converted PDF images and create a comprehensive profile using the Pre-Screening Criteria.
                                    </p>
                                </div>

                                <!-- AI Analysis Results -->
                                <div id="aiAnalysisResults" class="mb-4" style="display: none;">
                                    <!-- Results will be displayed here -->
                                </div>

                                <!-- PDF Viewer Container -->
                                <div id="pdfViewerContainer" style="height: 800px; border: 1px solid #ddd; border-radius: 8px; overflow-y: auto;">
                                    <div id="pdfViewer" class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading PDFs...</span>
                                        </div>
                                        <p class="mt-2">Loading PDF files...</p>
                                    </div>
                                </div>

                                <!-- Files List -->
                                <div class="mt-4">
                                    <h6 class="text-red">Files List</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th class="text-white">#</th>
                                                    <th class="text-white">File Title</th>
                                                    <th class="text-white">Description</th>
                                                    <th class="text-white">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($applicant_files as $index => $file): ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td>
                                                        <i class="fas fa-file-pdf text-danger"></i>
                                                        <?= esc($file['file_title']) ?>
                                                    </td>
                                                    <td><?= esc($file['file_description']) ?></td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-external-link-alt"></i> Open
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="analyzeIndividualFile(<?= $file['id'] ?>, '<?= esc($file['file_title']) ?>', '<?= esc($file['file_path']) ?>')">
                                                                <i class="fas fa-brain"></i> AI Analysis
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No files uploaded for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column - Pre-Screening Criteria -->
    <div class="col-lg-4">
        <div class="card hover-card sticky-criteria-card">
            <div class="card-header bg-yellow text-black">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check"></i> Pre-Screening Criteria
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($pre_screen_criteria)): ?>
                    <div class="criteria-list">
                        <?php foreach ($pre_screen_criteria as $index => $criteria): ?>
                            <div class="criteria-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="text-red mb-0">
                                        <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                        <?= esc($criteria['title'] ?? 'Criteria ' . ($index + 1)) ?>
                                    </h6>
                                    <span class="badge bg-<?= ($criteria['weight'] ?? 0) >= 70 ? 'danger' : (($criteria['weight'] ?? 0) >= 40 ? 'warning' : 'secondary') ?>">
                                        <?= esc($criteria['weight'] ?? 0) ?>%
                                    </span>
                                </div>
                                <p class="text-muted mb-2"><?= esc($criteria['description'] ?? '') ?></p>

                                <?php if (!empty($criteria['requirements'])): ?>
                                    <div class="requirements">
                                        <small class="text-muted d-block mb-1"><strong>Requirements:</strong></small>
                                        <ul class="list-unstyled ms-3">
                                            <?php foreach ($criteria['requirements'] as $requirement): ?>
                                                <li class="mb-1">
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    <small><?= esc($requirement) ?></small>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div class="criteria-actions mt-2">
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <button type="button" class="btn btn-outline-success" onclick="markCriteria(<?= $index ?>, 'pass')">
                                            <i class="fas fa-check"></i> Pass
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="markCriteria(<?= $index ?>, 'fail')">
                                            <i class="fas fa-times"></i> Fail
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Overall Assessment -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="text-red">Overall Assessment</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-success" id="passedCount">0</div>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-danger" id="failedCount">0</div>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-warning" id="pendingCount"><?= count($pre_screen_criteria) ?></div>
                                    <small class="text-muted">Pending</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <small class="text-muted">Completion Progress</small>
                        </div>

                        <!-- Pre-Screening Form -->
                        <form id="preScreeningForm" class="mt-4">
                            <?= csrf_field() ?>
                            <input type="hidden" name="applicant_id" value="<?= $applicant_id ?>">
                            <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                            <input type="hidden" name="pre_screened_ai_analysis" id="aiAnalysisData">
                            <input type="hidden" name="pre_screened_criteria_results" id="criteriaResultsData">

                            <div class="mb-3">
                                <label for="pre_screened_status" class="form-label">
                                    <strong>Pre-Screening Status <span class="text-danger">*</span></strong>
                                </label>
                                <select class="form-select" name="pre_screened_status" id="pre_screened_status" required>
                                    <option value="">Select Status</option>
                                    <option value="passed">Passed</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="pre_screened_remarks" class="form-label">
                                    <strong>Remarks</strong>
                                </label>
                                <textarea class="form-control" name="pre_screened_remarks" id="pre_screened_remarks"
                                          rows="4" placeholder="Enter your pre-screening remarks and observations..."></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="savePreScreeningBtn">
                                    <i class="fas fa-save me-2"></i>Save Pre-Screening Results
                                </button>
                            </div>
                        </form>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No pre-screening criteria defined for this exercise.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<style>
/* Fix tab text colors for better visibility */
.nav-tabs .nav-link {
    color: #333 !important;
}

.nav-tabs .nav-link.active {
    color: #000 !important;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    color: #F00F00 !important;
}

/* Sticky Pre-Screening Criteria Card */
.sticky-criteria-card {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    z-index: 1000;
}
</style>

<script>
// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// Criteria tracking
let criteriaResults = {};
const totalCriteria = <?= count($pre_screen_criteria) ?>;

$(document).ready(function() {
    // Load PDFs immediately on page load
    loadAllPDFs();

    // Initialize criteria tracking
    updateCriteriaSummary();
});

// Load and convert all PDF files to images on page load
async function loadAllPDFs() {
    const pdfViewer = document.getElementById('pdfViewer');
    const files = <?= json_encode($applicant_files) ?>;

    if (!pdfViewer) {
        console.error('PDF viewer element not found');
        return;
    }

    if (files.length === 0) {
        pdfViewer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> No PDF files to display.</div>';
        return;
    }

    // Show loading state
    pdfViewer.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <p class="mt-2">Converting PDF files to images...</p>
            <p class="text-muted">Processing ${files.length} file(s)...</p>
        </div>
    `;

    try {
        let allImagesHtml = '';
        let totalPages = 0;
        let processedPages = 0;

        // First, count total pages for progress tracking
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;
                totalPages += pdf.numPages;
            } catch (error) {
                console.error('Error counting pages for:', file.file_title, error);
            }
        }

        // Process each file and convert pages to images
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            // Add file header
            allImagesHtml += `
                <div class="pdf-file-header bg-red text-white p-2 mb-3 rounded">
                    <h6 class="mb-0">
                        <i class="fas fa-file-pdf"></i> ${file.file_title || 'Unknown File'}
                        ${file.file_description ? `<small class="ms-2">(${file.file_description})</small>` : ''}
                    </h6>
                </div>
            `;

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;

                // Convert each page to image
                for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    // Update progress
                    processedPages++;
                    const progress = Math.round((processedPages / totalPages) * 100);

                    pdfViewer.innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <p class="mt-2">Converting PDF pages to images...</p>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar bg-red" role="progressbar" style="width: ${progress}%" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                                    ${progress}%
                                </div>
                            </div>
                            <p class="text-muted">Processing page ${processedPages} of ${totalPages}</p>
                        </div>
                    `;

                    const page = await pdf.getPage(pageNum);
                    const viewport = page.getViewport({ scale: 1.5 }); // Higher scale for better quality

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert canvas to high-quality image
                    const imageDataUrl = canvas.toDataURL('image/png', 1.0);

                    allImagesHtml += `
                        <div class="pdf-page-image mb-4 text-center" data-file-index="${i}" data-page="${pageNum}">
                            <div class="page-info bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <strong>${file.file_title}</strong> - Page ${pageNum} of ${pdf.numPages}
                                </small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'in')" title="Zoom In">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'out')" title="Zoom Out">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'reset')" title="Reset Zoom">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="image-container" style="overflow: auto; max-height: 800px;">
                                <img src="${imageDataUrl}"
                                     class="pdf-page-img img-fluid border rounded shadow-sm"
                                     style="max-width: 100%; cursor: zoom-in;"
                                     onclick="toggleFullscreen(this)"
                                     alt="Page ${pageNum} of ${file.file_title}" />
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error converting PDF to images:', file.file_title, error);
                allImagesHtml += `
                    <div class="alert alert-warning mb-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        Could not convert PDF to images: ${file.file_title || 'Unknown File'}
                        <br><small>Error: ${error.message}</small>
                    </div>
                `;
            }
        }

        // Display all converted images
        if (pdfViewer) {
            pdfViewer.innerHTML = allImagesHtml;

            // Add completion message
            const completionMessage = `
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle"></i>
                    Successfully converted ${totalPages} pages from ${files.length} PDF file(s) to images.
                </div>
            `;
            pdfViewer.insertAdjacentHTML('afterbegin', completionMessage);
        }

    } catch (error) {
        console.error('Error converting PDFs to images:', error);
        if (pdfViewer) {
            pdfViewer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    Error converting PDF files to images: ${error.message}
                </div>
            `;
        }
    }
}

// Mark criteria as pass/fail
function markCriteria(index, result) {
    criteriaResults[index] = result;

    // Update button states
    const criteriaItem = document.querySelectorAll('.criteria-item')[index];
    const buttons = criteriaItem.querySelectorAll('.btn-group button');

    buttons.forEach(btn => btn.classList.remove('active'));

    if (result === 'pass') {
        buttons[0].classList.add('active', 'btn-success');
        buttons[0].classList.remove('btn-outline-success');
        buttons[1].classList.add('btn-outline-danger');
        buttons[1].classList.remove('btn-danger');
    } else {
        buttons[1].classList.add('active', 'btn-danger');
        buttons[1].classList.remove('btn-outline-danger');
        buttons[0].classList.add('btn-outline-success');
        buttons[0].classList.remove('btn-success');
    }

    updateCriteriaSummary();
}

// Update criteria summary
function updateCriteriaSummary() {
    const passed = Object.values(criteriaResults).filter(r => r === 'pass').length;
    const failed = Object.values(criteriaResults).filter(r => r === 'fail').length;
    const pending = totalCriteria - passed - failed;

    const passedElement = document.getElementById('passedCount');
    const failedElement = document.getElementById('failedCount');
    const pendingElement = document.getElementById('pendingCount');
    const progressElement = document.getElementById('progressBar');

    if (passedElement) passedElement.textContent = passed;
    if (failedElement) failedElement.textContent = failed;
    if (pendingElement) pendingElement.textContent = pending;

    const progress = totalCriteria > 0 ? ((passed + failed) / totalCriteria) * 100 : 0;
    if (progressElement) progressElement.style.width = progress + '%';
}

// Image zoom functionality
function zoomImage(button, action) {
    const imageContainer = button.closest('.pdf-page-image').querySelector('.image-container');
    const image = imageContainer.querySelector('.pdf-page-img');

    let currentScale = parseFloat(image.dataset.scale || '1');

    switch(action) {
        case 'in':
            currentScale = Math.min(currentScale * 1.2, 3); // Max 3x zoom
            break;
        case 'out':
            currentScale = Math.max(currentScale / 1.2, 0.5); // Min 0.5x zoom
            break;
        case 'reset':
            currentScale = 1;
            break;
    }

    image.dataset.scale = currentScale;
    image.style.transform = `scale(${currentScale})`;
    image.style.transformOrigin = 'center';

    // Update cursor based on zoom level
    if (currentScale > 1) {
        image.style.cursor = 'zoom-out';
    } else {
        image.style.cursor = 'zoom-in';
    }
}

// Toggle fullscreen for images
function toggleFullscreen(image) {
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        const container = image.closest('.pdf-page-image');
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }
    }
}

// AI File Analysis Function
async function analyzeFile(fileId, fileName, filePath) {
    // Show loading modal
    Swal.fire({
        title: 'Analyzing File with AI',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p>AI is analyzing: <strong>${fileName}</strong></p>
                <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                <p class="text-muted">Reading text, images, tables, and charts...</p>
            </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        // Convert PDF to base64 for multimodal analysis
        const fullFilePath = '<?= base_url() ?>' + filePath;
        const pdfBase64 = await convertPDFToBase64(fullFilePath);

        if (!pdfBase64) {
            throw new Error('Could not convert PDF file for AI analysis');
        }

        // Send to AI for multimodal analysis
        const analysisResult = await sendToGeminiMultimodal(pdfBase64, fileName);

        // Show results
        Swal.fire({
            title: 'AI Analysis Complete',
            html: `
                <div class="text-start">
                    <h6 class="text-primary mb-3">File: ${fileName}</h6>
                    <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                        <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${JSON.stringify(analysisResult, null, 2)}</pre>
                    </div>
                </div>
            `,
            width: '80%',
            confirmButtonText: 'Close',
            confirmButtonColor: '#F00F00'
        });

    } catch (error) {
        console.error('Error analyzing file:', error);
        Swal.fire({
            title: 'Analysis Failed',
            text: `Error analyzing file: ${error.message}`,
            icon: 'error',
            confirmButtonColor: '#F00F00'
        });
    }
}

// Convert PDF to base64 for multimodal analysis
async function convertPDFToBase64(pdfUrl) {
    try {
        const response = await fetch(pdfUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        // Convert to base64
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }

        return btoa(binary);
    } catch (error) {
        console.error('Error converting PDF to base64:', error);
        throw new Error('Failed to convert PDF to base64: ' + error.message);
    }
}

// Send PDF to Gemini Flash 2.5 for multimodal analysis
async function sendToGeminiMultimodal(pdfBase64, fileName) {
    const prompt = `
Please analyze this PDF document comprehensively using your multimodal capabilities. Read and extract information from:
- All text content
- Images, photos, and graphics
- Tables and charts
- Diagrams and visual elements
- Layout and formatting

DOCUMENT: ${fileName}

Provide a structured JSON response with the applicant's complete profile information:

{
    "document_type": "CV/Resume/Cover Letter/Certificate/Transcript/Portfolio/Other",
    "document_analysis": {
        "total_pages": 0,
        "has_images": false,
        "has_tables": false,
        "has_charts": false,
        "layout_quality": "professional/basic/poor"
    },
    "personal_information": {
        "name": "",
        "email": "",
        "phone": "",
        "address": "",
        "date_of_birth": "",
        "nationality": "",
        "linkedin_profile": "",
        "photo_present": false
    },
    "education": [
        {
            "institution": "",
            "degree": "",
            "field_of_study": "",
            "graduation_year": "",
            "grade": "",
            "honors": "",
            "relevant_coursework": []
        }
    ],
    "work_experience": [
        {
            "company": "",
            "position": "",
            "start_date": "",
            "end_date": "",
            "duration": "",
            "responsibilities": [],
            "achievements": [],
            "technologies_used": []
        }
    ],
    "skills": {
        "technical_skills": [],
        "soft_skills": [],
        "languages": [],
        "software_proficiency": [],
        "programming_languages": []
    },
    "certifications": [
        {
            "name": "",
            "issuing_organization": "",
            "date_obtained": "",
            "expiry_date": "",
            "credential_id": ""
        }
    ],
    "projects": [
        {
            "name": "",
            "description": "",
            "technologies": [],
            "duration": "",
            "role": ""
        }
    ],
    "achievements_awards": [],
    "publications": [],
    "references": [
        {
            "name": "",
            "position": "",
            "company": "",
            "contact": "",
            "relationship": ""
        }
    ],
    "visual_elements": {
        "charts_data": [],
        "images_description": [],
        "tables_content": []
    },
    "summary": "Comprehensive summary of the candidate's profile",
    "key_strengths": [],
    "areas_of_expertise": [],
    "career_progression": "",
    "overall_assessment": {
        "experience_level": "entry/mid/senior/executive",
        "profile_completeness": "excellent/good/fair/poor",
        "presentation_quality": "excellent/good/fair/poor"
    }
}

Extract ALL information present in the document, including data from images, tables, charts, and visual elements. If a field is not available, use null or an empty array.
`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [
                    {
                        text: prompt
                    },
                    {
                        inline_data: {
                            mime_type: "application/pdf",
                            data: pdfBase64
                        }
                    }
                ]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.95,
                maxOutputTokens: 4096,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}

// Format Date of Birth and Calculate Age
function formatDateOfBirth() {
    const dobElement = document.getElementById('dobDisplay');
    const ageElement = document.getElementById('ageDisplay');

    if (dobElement && dobElement.textContent.trim()) {
        const dobValue = dobElement.textContent.trim();

        // Parse the date (assuming it's in YYYY-MM-DD format from database)
        const dobDate = new Date(dobValue);

        if (!isNaN(dobDate.getTime())) {
            // Format as dd/mm/yyyy
            const day = String(dobDate.getDate()).padStart(2, '0');
            const month = String(dobDate.getMonth() + 1).padStart(2, '0');
            const year = dobDate.getFullYear();
            const formattedDate = `${day}/${month}/${year}`;

            // Calculate age
            const today = new Date();
            let age = today.getFullYear() - dobDate.getFullYear();
            const monthDiff = today.getMonth() - dobDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
                age--;
            }

            // Update display
            dobElement.textContent = formattedDate;
            ageElement.textContent = `(${age} years old)`;
        }
    }
}

// Display Children in Tabular Format
function displayChildren() {
    const childrenContainer = document.getElementById('childrenDisplay');
    const childrenData = <?= json_encode($firstApp['children'] ?? '') ?>;

    if (childrenContainer && childrenData) {
        try {
            // Try to parse as JSON if it's a string
            let children = [];
            if (typeof childrenData === 'string') {
                if (childrenData.trim() === '' || childrenData.trim() === 'null') {
                    childrenContainer.innerHTML = '<span class="text-muted">No children information provided</span>';
                    return;
                }
                try {
                    children = JSON.parse(childrenData);
                } catch (e) {
                    // If not JSON, treat as plain text
                    childrenContainer.innerHTML = `<span class="text-muted">${childrenData}</span>`;
                    return;
                }
            } else if (Array.isArray(childrenData)) {
                children = childrenData;
            }

            if (children && children.length > 0) {
                let tableHTML = `
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Date of Birth</th>
                                    <th>Gender</th>
                                    <th>Age</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                children.forEach((child, index) => {
                    let age = '';
                    if (child.dob) {
                        const childDob = new Date(child.dob);
                        if (!isNaN(childDob.getTime())) {
                            const today = new Date();
                            let childAge = today.getFullYear() - childDob.getFullYear();
                            const monthDiff = today.getMonth() - childDob.getMonth();

                            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < childDob.getDate())) {
                                childAge--;
                            }
                            age = `${childAge} years`;

                            // Format child DOB as dd/mm/yyyy
                            const day = String(childDob.getDate()).padStart(2, '0');
                            const month = String(childDob.getMonth() + 1).padStart(2, '0');
                            const year = childDob.getFullYear();
                            child.dob = `${day}/${month}/${year}`;
                        }
                    }

                    tableHTML += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${child.name || 'Not specified'}</td>
                            <td>${child.dob || 'Not specified'}</td>
                            <td>${child.gender || 'Not specified'}</td>
                            <td>${age || 'Not specified'}</td>
                        </tr>
                    `;
                });

                tableHTML += `
                            </tbody>
                        </table>
                    </div>
                `;

                childrenContainer.innerHTML = tableHTML;
            } else {
                childrenContainer.innerHTML = '<span class="text-muted">No children information provided</span>';
            }
        } catch (error) {
            console.error('Error displaying children:', error);
            childrenContainer.innerHTML = '<span class="text-muted">Error displaying children information</span>';
        }
    }
}

// Global variable to store converted images for AI analysis
let convertedImages = [];

// Analyze All Files with AI using Pre-Screening Criteria
async function analyzeAllFilesWithAI() {
    const analyzeBtn = document.getElementById('analyzeFilesBtn');
    const resultsContainer = document.getElementById('aiAnalysisResults');

    // Disable button and show loading state
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing with AI...';

    // Show loading in results container
    resultsContainer.style.display = 'block';
    resultsContainer.innerHTML = `
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-brain me-2"></i>AI Analysis in Progress</h6>
            </div>
            <div class="card-body text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p class="mb-2">AI is analyzing all PDF files using Pre-Screening Criteria...</p>
                <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                <p class="text-muted">This may take a few minutes depending on file size and complexity.</p>
            </div>
        </div>
    `;

    try {
        // Get pre-screening criteria
        const preScreenCriteria = <?= json_encode($pre_screen_criteria) ?>;

        // Get all converted images from the PDF viewer
        const pdfViewer = document.getElementById('pdfViewer');
        const images = pdfViewer.querySelectorAll('img');

        if (images.length === 0) {
            throw new Error('No converted images found. Please wait for PDF conversion to complete.');
        }

        // Convert images to base64 for AI analysis
        const imageDataArray = [];
        for (let img of images) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            ctx.drawImage(img, 0, 0);
            const base64 = canvas.toDataURL('image/png').split(',')[1];
            imageDataArray.push(base64);
        }

        // Send to AI for comprehensive analysis
        const analysisResult = await sendToGeminiForPreScreening(imageDataArray, preScreenCriteria);

        // Display results
        displayAIAnalysisResults(analysisResult, preScreenCriteria);

        // Populate hidden fields with AI analysis data
        populateHiddenFields(analysisResult, preScreenCriteria);

    } catch (error) {
        console.error('Error analyzing files with AI:', error);
        resultsContainer.innerHTML = `
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Analysis Failed</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                    <p class="text-muted">Please try again or contact support if the problem persists.</p>
                </div>
            </div>
        `;
    } finally {
        // Re-enable button
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-brain me-2"></i>Analyse Files with AI';
    }
}

// Send images to Gemini for pre-screening analysis
async function sendToGeminiForPreScreening(imageDataArray, preScreenCriteria) {
    // Create criteria text for the prompt
    let criteriaText = '';
    preScreenCriteria.forEach((criteria, index) => {
        criteriaText += `${index + 1}. ${criteria.title || 'Criteria ' + (index + 1)}\n`;
        if (criteria.description) {
            criteriaText += `   Description: ${criteria.description}\n`;
        }
        if (criteria.weight) {
            criteriaText += `   Weight: ${criteria.weight}\n`;
        }
        criteriaText += '\n';
    });

    const prompt = `
You are an expert HR analyst conducting a comprehensive pre-screening evaluation. Analyze the provided document images (CV, certificates, transcripts, etc.) and evaluate the applicant against the specific pre-screening criteria.

PRE-SCREENING CRITERIA TO EVALUATE:
${criteriaText}

ANALYSIS INSTRUCTIONS:
1. Carefully examine all document images provided
2. Extract relevant information from text, tables, images, and visual elements
3. Evaluate the applicant against each pre-screening criterion
4. Provide detailed evidence from the documents to support your evaluation
5. Give a clear recommendation (MEETS/DOES NOT MEET/PARTIALLY MEETS) for each criterion

Please provide a comprehensive JSON response with the following structure:

{
    "analysis_summary": {
        "total_documents_analyzed": 0,
        "analysis_date": "${new Date().toISOString()}",
        "overall_recommendation": "RECOMMENDED/NOT RECOMMENDED/REQUIRES REVIEW",
        "confidence_score": "0-100%"
    },
    "applicant_profile": {
        "personal_information": {
            "name": "",
            "email": "",
            "phone": "",
            "address": "",
            "date_of_birth": "",
            "nationality": ""
        },
        "education": [
            {
                "institution": "",
                "degree": "",
                "field_of_study": "",
                "graduation_year": "",
                "grade": ""
            }
        ],
        "work_experience": [
            {
                "company": "",
                "position": "",
                "duration": "",
                "responsibilities": []
            }
        ],
        "skills_and_qualifications": [],
        "certifications": [],
        "achievements": []
    },
    "criteria_evaluation": [
        {
            "criterion_number": 1,
            "criterion_title": "",
            "evaluation_result": "MEETS/DOES NOT MEET/PARTIALLY MEETS",
            "evidence_found": [],
            "detailed_analysis": "",
            "score": "0-100",
            "recommendation": ""
        }
    ],
    "overall_assessment": {
        "strengths": [],
        "weaknesses": [],
        "areas_for_clarification": [],
        "final_recommendation": "",
        "areas_for_clarification": []
    }
}

Analyze thoroughly and provide detailed, evidence-based evaluations for each criterion.
`;

    // Prepare the request body with images
    const parts = [{ text: prompt }];

    // Add all images to the request
    imageDataArray.forEach(imageData => {
        parts.push({
            inline_data: {
                mime_type: "image/png",
                data: imageData
            }
        });
    });

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: parts
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.95,
                maxOutputTokens: 8192,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts[0]) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}

// Display AI Analysis Results
function displayAIAnalysisResults(analysisResult, preScreenCriteria) {
    const resultsContainer = document.getElementById('aiAnalysisResults');

    if (analysisResult.error) {
        resultsContainer.innerHTML = `
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Analysis Completed with Issues</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Error:</strong> ${analysisResult.error}
                    </div>
                    <details>
                        <summary>Raw AI Response</summary>
                        <pre class="mt-2 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.9em;">${analysisResult.raw_response || 'No response available'}</pre>
                    </details>
                </div>
            </div>
        `;
        return;
    }

    // Build the results HTML
    let resultsHTML = `
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>AI Analysis Complete</h6>
            </div>
            <div class="card-body">
    `;

    // Analysis Summary
    if (analysisResult.analysis_summary) {
        const summary = analysisResult.analysis_summary;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Analysis Summary</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-primary">${summary.total_documents_analyzed || 'N/A'}</div>
                                <small class="text-muted">Documents Analyzed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 ${summary.overall_recommendation === 'RECOMMENDED' ? 'text-success' : summary.overall_recommendation === 'NOT RECOMMENDED' ? 'text-danger' : 'text-warning'}">${summary.overall_recommendation || 'N/A'}</div>
                                <small class="text-muted">Overall Recommendation</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-info">${summary.confidence_score || 'N/A'}</div>
                                <small class="text-muted">Confidence Score</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h6 text-secondary">${summary.analysis_date ? new Date(summary.analysis_date).toLocaleString() : 'N/A'}</div>
                                <small class="text-muted">Analysis Date</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Criteria Evaluation
    if (analysisResult.criteria_evaluation && analysisResult.criteria_evaluation.length > 0) {
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Pre-Screening Criteria Evaluation</h6>
                    <div class="accordion" id="criteriaAccordion">
        `;

        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            const badgeClass = evaluation.evaluation_result === 'MEETS' ? 'bg-success' :
                              evaluation.evaluation_result === 'DOES NOT MEET' ? 'bg-danger' : 'bg-warning';

            resultsHTML += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading${index}">
                        <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                            <span class="badge ${badgeClass} me-3">${evaluation.evaluation_result || 'N/A'}</span>
                            <strong>${evaluation.criterion_title || 'Criterion ' + (index + 1)}</strong>
                            ${evaluation.score ? `<span class="badge bg-secondary ms-auto me-3">${evaluation.score}/100</span>` : ''}
                        </button>
                    </h2>
                    <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#criteriaAccordion">
                        <div class="accordion-body">
                            ${evaluation.detailed_analysis ? `<p><strong>Analysis:</strong> ${evaluation.detailed_analysis}</p>` : ''}
                            ${evaluation.evidence_found && evaluation.evidence_found.length > 0 ? `
                                <p><strong>Evidence Found:</strong></p>
                                <ul>
                                    ${evaluation.evidence_found.map(evidence => `<li>${evidence}</li>`).join('')}
                                </ul>
                            ` : ''}
                            ${evaluation.recommendation ? `<p><strong>Recommendation:</strong> ${evaluation.recommendation}</p>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Applicant Profile Summary
    if (analysisResult.applicant_profile) {
        const profile = analysisResult.applicant_profile;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Extracted Applicant Profile</h6>
                    <div class="row">
        `;

        // Personal Information
        if (profile.personal_information) {
            resultsHTML += `
                <div class="col-md-6">
                    <h6 class="text-secondary">Personal Information</h6>
                    <ul class="list-unstyled">
                        ${profile.personal_information.name ? `<li><strong>Name:</strong> ${profile.personal_information.name}</li>` : ''}
                        ${profile.personal_information.email ? `<li><strong>Email:</strong> ${profile.personal_information.email}</li>` : ''}
                        ${profile.personal_information.phone ? `<li><strong>Phone:</strong> ${profile.personal_information.phone}</li>` : ''}
                        ${profile.personal_information.date_of_birth ? `<li><strong>Date of Birth:</strong> ${profile.personal_information.date_of_birth}</li>` : ''}
                    </ul>
                </div>
            `;
        }

        // Skills and Qualifications
        if (profile.skills_and_qualifications && profile.skills_and_qualifications.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <h6 class="text-secondary">Key Skills & Qualifications</h6>
                    <ul>
                        ${profile.skills_and_qualifications.map(skill => `<li>${skill}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Overall Assessment
    if (analysisResult.overall_assessment) {
        const assessment = analysisResult.overall_assessment;
        resultsHTML += `
            <div class="row">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Overall Assessment</h6>
                    <div class="row">
        `;

        if (assessment.strengths && assessment.strengths.length > 0) {
            resultsHTML += `
                <div class="col-md-4">
                    <h6 class="text-success">Strengths</h6>
                    <ul class="list-group list-group-flush">
                        ${assessment.strengths.map(strength => `<li class="list-group-item border-0 px-0"><i class="fas fa-check text-success me-2"></i>${strength}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        if (assessment.weaknesses && assessment.weaknesses.length > 0) {
            resultsHTML += `
                <div class="col-md-4">
                    <h6 class="text-danger">Areas for Improvement</h6>
                    <ul class="list-group list-group-flush">
                        ${assessment.weaknesses.map(weakness => `<li class="list-group-item border-0 px-0"><i class="fas fa-exclamation-triangle text-warning me-2"></i>${weakness}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // Next Steps section removed as requested

        resultsHTML += `
                    </div>
        `;

        if (assessment.final_recommendation) {
            resultsHTML += `
                <div class="mt-3">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Final Recommendation</h6>
                        <p class="mb-0">${assessment.final_recommendation}</p>
                    </div>
                </div>
            `;
        }

        resultsHTML += `
                </div>
            </div>
        `;
    }

    resultsHTML += `
            </div>
        </div>
    `;

    resultsContainer.innerHTML = resultsHTML;
}

// Populate hidden fields with AI analysis data
function populateHiddenFields(analysisResult, preScreenCriteria) {
    // Populate AI analysis data
    const aiAnalysisField = document.getElementById('aiAnalysisData');
    if (aiAnalysisField) {
        aiAnalysisField.value = JSON.stringify(analysisResult);
    }

    // Create criteria results data
    const criteriaResults = {
        analysis_date: new Date().toISOString(),
        criteria_evaluations: [],
        overall_summary: {
            total_criteria: preScreenCriteria.length,
            overall_recommendation: analysisResult.analysis_summary?.overall_recommendation || 'PENDING',
            confidence_score: analysisResult.analysis_summary?.confidence_score || 'N/A'
        }
    };

    // Process each criterion evaluation
    if (analysisResult.criteria_evaluation) {
        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            criteriaResults.criteria_evaluations.push({
                criterion_number: index + 1,
                criterion_title: evaluation.criterion_title || preScreenCriteria[index]?.title || `Criteria ${index + 1}`,
                evaluation_result: evaluation.evaluation_result || 'PENDING',
                score: evaluation.score || 0,
                evidence_found: evaluation.evidence_found || [],
                detailed_analysis: evaluation.detailed_analysis || '',
                recommendation: evaluation.recommendation || ''
            });
        });
    }

    // Populate criteria results data
    const criteriaResultsField = document.getElementById('criteriaResultsData');
    if (criteriaResultsField) {
        criteriaResultsField.value = JSON.stringify(criteriaResults);
    }
}

// Handle pre-screening form submission
document.addEventListener('DOMContentLoaded', function() {
    formatDateOfBirth();
    displayChildren();

    // Add form submission handler
    const preScreeningForm = document.getElementById('preScreeningForm');
    if (preScreeningForm) {
        preScreeningForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const saveBtn = document.getElementById('savePreScreeningBtn');
            const originalText = saveBtn.innerHTML;

            // Show loading state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

            try {
                const formData = new FormData(this);

                const response = await fetch('<?= base_url('application_pre_screening/save_prescreening_results') ?>', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Pre-screening results saved successfully.',
                        icon: 'success',
                        confirmButtonColor: '#F00F00'
                    });
                } else {
                    throw new Error(result.message || 'Failed to save pre-screening results');
                }

            } catch (error) {
                console.error('Error saving pre-screening results:', error);
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to save pre-screening results. Please try again.',
                    icon: 'error',
                    confirmButtonColor: '#F00F00'
                });
            } finally {
                // Restore button state
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
            }
        });
    }
});

// Individual File AI Analysis Function
async function analyzeIndividualFile(fileId, fileName, filePath) {
    // Show loading modal
    Swal.fire({
        title: 'Analyzing Individual File',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p>AI is creating detailed profile for: <strong>${fileName}</strong></p>
                <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                <p class="text-muted">Extracting comprehensive file profile...</p>
            </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        // Load and analyze PDF structure first
        const fullFilePath = '<?= base_url() ?>' + filePath;
        const pdf = await pdfjsLib.getDocument(fullFilePath).promise;
        const totalPages = pdf.numPages;

        let profileResult;

        if (totalPages > 20) {
            // Update loading message for large documents
            Swal.update({
                html: `
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status"></div>
                        <p>Large document detected: <strong>${totalPages} pages</strong></p>
                        <p>AI is analyzing: <strong>${fileName}</strong></p>
                        <p class="text-muted">Splitting into ${Math.ceil(totalPages / 10)} sets of 10 pages each...</p>
                        <p class="text-muted">This may take several minutes for comprehensive analysis.</p>
                        <div class="progress mt-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%" id="analysisProgress"></div>
                        </div>
                        <small class="text-muted">Processing progress</small>
                    </div>
                `
            });

            // Split document and analyze in chunks
            profileResult = await analyzeDocumentInChunks(pdf, fileName, totalPages);
        } else {
            // Standard analysis for smaller documents
            const pdfBase64 = await convertPDFToBase64(fullFilePath);
            if (!pdfBase64) {
                throw new Error('Could not convert PDF file for AI analysis');
            }
            profileResult = await sendToGeminiForFileProfile(pdfBase64, fileName, 1, totalPages, totalPages);
        }

        // Display results in a comprehensive modal
        displayFileProfileResults(profileResult, fileName);

    } catch (error) {
        console.error('Error analyzing individual file:', error);
        Swal.fire({
            title: 'Analysis Failed',
            text: `Error analyzing file: ${error.message}`,
            icon: 'error',
            confirmButtonColor: '#F00F00'
        });
    }
}

// Analyze document in chunks for large files
async function analyzeDocumentInChunks(pdf, fileName, totalPages) {
    const chunkSize = 10;
    const totalChunks = Math.ceil(totalPages / chunkSize);
    const chunkResults = [];

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const startPage = chunkIndex * chunkSize + 1;
        const endPage = Math.min((chunkIndex + 1) * chunkSize, totalPages);

        // Update progress
        const progress = ((chunkIndex + 1) / totalChunks) * 100;
        const progressBar = document.getElementById('analysisProgress');
        if (progressBar) {
            progressBar.style.width = progress + '%';
            progressBar.textContent = `${Math.round(progress)}%`;
        }

        // Update status message
        Swal.update({
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <p>Analyzing chunk ${chunkIndex + 1} of ${totalChunks}</p>
                    <p>Pages ${startPage}-${endPage} of ${totalPages}</p>
                    <p class="text-muted">File: <strong>${fileName}</strong></p>
                    <div class="progress mt-3">
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${progress}%">${Math.round(progress)}%</div>
                    </div>
                    <small class="text-muted">Processing progress</small>
                </div>
            `
        });

        try {
            // Extract pages for this chunk
            const chunkBase64 = await extractPagesAsBase64(pdf, startPage, endPage);

            // Analyze this chunk
            const chunkResult = await sendToGeminiForFileProfile(
                chunkBase64,
                fileName,
                chunkIndex + 1,
                totalChunks,
                totalPages,
                startPage,
                endPage
            );

            chunkResults.push({
                chunk_number: chunkIndex + 1,
                pages_range: `${startPage}-${endPage}`,
                analysis: chunkResult
            });

        } catch (error) {
            console.error(`Error analyzing chunk ${chunkIndex + 1}:`, error);
            chunkResults.push({
                chunk_number: chunkIndex + 1,
                pages_range: `${startPage}-${endPage}`,
                error: error.message,
                analysis: null
            });
        }
    }

    // Combine all chunk results into a comprehensive profile
    return combineChunkResults(chunkResults, fileName, totalPages);
}

// Extract specific pages from PDF as base64
async function extractPagesAsBase64(pdf, startPage, endPage) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let combinedImageData = [];

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: 1.5 });

        canvas.width = viewport.width;
        canvas.height = viewport.height;

        await page.render({
            canvasContext: context,
            viewport: viewport
        }).promise;

        // Convert page to base64
        const pageImageData = canvas.toDataURL('image/png', 1.0);
        combinedImageData.push(pageImageData.split(',')[1]); // Remove data:image/png;base64, prefix
    }

    // For now, we'll send the first page of the chunk as representative
    // In a more advanced implementation, you could combine images or send multiple
    return combinedImageData[0];
}

// Combine chunk results into comprehensive profile
function combineChunkResults(chunkResults, fileName, totalPages) {
    const combinedProfile = {
        file_metadata: {
            file_name: fileName,
            analysis_date: new Date().toISOString(),
            document_type: "Multi-part Document",
            total_pages: totalPages,
            analysis_method: "Chunked Analysis",
            total_chunks: chunkResults.length,
            chunks_analyzed: chunkResults.filter(chunk => chunk.analysis && !chunk.error).length
        },
        chunk_analyses: chunkResults,
        combined_insights: {
            personal_information: {},
            professional_profile: {},
            education_details: [],
            work_experience: [],
            skills_competencies: {
                technical_skills: [],
                soft_skills: [],
                certifications: []
            },
            document_quality_assessment: {
                overall_completeness: "High",
                chunk_consistency: "Good",
                information_distribution: "Comprehensive"
            }
        },
        ai_analysis_summary: {
            overall_assessment: `Comprehensive ${totalPages}-page document analyzed in ${chunkResults.length} chunks`,
            analysis_coverage: `${chunkResults.filter(chunk => chunk.analysis && !chunk.error).length}/${chunkResults.length} chunks successfully analyzed`,
            notable_features: [],
            recommended_follow_up: [
                "Review individual chunk analyses for detailed insights",
                "Cross-reference information across chunks for consistency",
                "Focus on chunks with highest information density"
            ]
        }
    };

    // Merge information from successful chunk analyses
    chunkResults.forEach(chunk => {
        if (chunk.analysis && !chunk.error) {
            const analysis = chunk.analysis;

            // Merge personal information (prioritize non-empty values)
            if (analysis.personal_information) {
                Object.keys(analysis.personal_information).forEach(key => {
                    if (analysis.personal_information[key] && !combinedProfile.combined_insights.personal_information[key]) {
                        combinedProfile.combined_insights.personal_information[key] = analysis.personal_information[key];
                    }
                });
            }

            // Merge skills (avoid duplicates)
            if (analysis.skills_competencies) {
                if (analysis.skills_competencies.technical_skills) {
                    analysis.skills_competencies.technical_skills.forEach(skill => {
                        if (!combinedProfile.combined_insights.skills_competencies.technical_skills.includes(skill)) {
                            combinedProfile.combined_insights.skills_competencies.technical_skills.push(skill);
                        }
                    });
                }
                if (analysis.skills_competencies.soft_skills) {
                    analysis.skills_competencies.soft_skills.forEach(skill => {
                        if (!combinedProfile.combined_insights.skills_competencies.soft_skills.includes(skill)) {
                            combinedProfile.combined_insights.skills_competencies.soft_skills.push(skill);
                        }
                    });
                }
            }

            // Collect notable features
            if (analysis.ai_analysis_summary && analysis.ai_analysis_summary.notable_features) {
                combinedProfile.ai_analysis_summary.notable_features.push(
                    ...analysis.ai_analysis_summary.notable_features.map(feature =>
                        `Chunk ${chunk.chunk_number} (${chunk.pages_range}): ${feature}`
                    )
                );
            }
        }
    });

    return combinedProfile;
}

// Send PDF to Gemini for detailed file profiling
async function sendToGeminiForFileProfile(pdfBase64, fileName, chunkNumber = 1, totalChunks = 1, totalPages = 0, startPage = 1, endPage = 0) {
    const chunkInfo = totalChunks > 1 ?
        `\n\nCHUNK ANALYSIS INFO:
- This is chunk ${chunkNumber} of ${totalChunks} total chunks
- Analyzing pages ${startPage}-${endPage} of ${totalPages} total pages
- Focus on extracting information specific to this section
- Note any incomplete information that may continue in other chunks` : '';

    const prompt = `
You are an expert document analyst. Analyze this PDF document ${totalChunks > 1 ? 'chunk' : 'comprehensively'} and create a detailed profile that will help other AI systems quickly understand the file contents and determine its relevance for various analyses.

DOCUMENT: ${fileName}${chunkInfo}

Create a comprehensive JSON profile with the following structure:

{
    "file_metadata": {
        "file_name": "${fileName}",
        "analysis_date": "${new Date().toISOString()}",
        "document_type": "CV/Resume/Cover Letter/Certificate/Transcript/Portfolio/Reference Letter/Other",
        "document_category": "Personal Document/Educational Document/Professional Document/Certification/Other",
        "primary_purpose": "Brief description of document's main purpose",
        "confidence_level": "High/Medium/Low"${totalChunks > 1 ? `,
        "chunk_info": {
            "chunk_number": ${chunkNumber},
            "total_chunks": ${totalChunks},
            "pages_analyzed": "${startPage}-${endPage}",
            "total_document_pages": ${totalPages},
            "is_partial_analysis": true
        }` : ''}
    },
    "document_structure": {
        "total_pages": 0,
        "has_images": false,
        "has_tables": false,
        "has_charts": false,
        "has_signatures": false,
        "has_letterhead": false,
        "layout_quality": "Professional/Standard/Basic/Poor",
        "text_density": "High/Medium/Low",
        "visual_elements": []
    },
    "content_summary": {
        "main_topics": [],
        "key_sections": [],
        "important_dates": [],
        "organizations_mentioned": [],
        "locations_mentioned": [],
        "contact_information": {
            "emails": [],
            "phones": [],
            "addresses": []
        }
    },
    "personal_information": {
        "full_name": "",
        "professional_title": "",
        "current_position": "",
        "current_employer": "",
        "email_address": "",
        "phone_number": "",
        "address": "",
        "date_of_birth": "",
        "nationality": "",
        "linkedin_profile": "",
        "other_social_profiles": []
    },
    "professional_profile": {
        "career_level": "Entry/Junior/Mid/Senior/Executive/Student",
        "years_of_experience": "",
        "industry_focus": [],
        "specializations": [],
        "current_role_summary": "",
        "career_objective": ""
    },
    "education_details": [
        {
            "institution_name": "",
            "degree_type": "",
            "field_of_study": "",
            "graduation_year": "",
            "grade_gpa": "",
            "honors_awards": "",
            "thesis_project": "",
            "relevant_coursework": []
        }
    ],
    "work_experience": [
        {
            "company_name": "",
            "position_title": "",
            "employment_period": "",
            "duration": "",
            "location": "",
            "key_responsibilities": [],
            "major_achievements": [],
            "technologies_tools": [],
            "team_size_managed": ""
        }
    ],
    "skills_competencies": {
        "technical_skills": [],
        "software_proficiency": [],
        "programming_languages": [],
        "languages_spoken": [],
        "soft_skills": [],
        "leadership_skills": [],
        "certifications": []
    },
    "certifications_licenses": [
        {
            "certification_name": "",
            "issuing_organization": "",
            "issue_date": "",
            "expiry_date": "",
            "credential_id": "",
            "verification_url": ""
        }
    ],
    "projects_achievements": [
        {
            "project_name": "",
            "description": "",
            "role": "",
            "technologies_used": [],
            "duration": "",
            "outcomes": []
        }
    ],
    "publications_research": [
        {
            "title": "",
            "publication_type": "",
            "journal_conference": "",
            "publication_date": "",
            "co_authors": [],
            "abstract": ""
        }
    ],
    "awards_recognition": [
        {
            "award_name": "",
            "awarding_organization": "",
            "date_received": "",
            "description": "",
            "significance": ""
        }
    ],
    "references": [
        {
            "name": "",
            "position": "",
            "organization": "",
            "contact_information": "",
            "relationship": ""
        }
    ],
    "document_quality_assessment": {
        "completeness_score": "0-100",
        "presentation_quality": "Excellent/Good/Fair/Poor",
        "information_clarity": "High/Medium/Low",
        "professional_formatting": true/false,
        "consistency": "High/Medium/Low",
        "potential_red_flags": []
    },
    "relevance_indicators": {
        "job_application_relevance": "High/Medium/Low",
        "academic_relevance": "High/Medium/Low",
        "professional_development": "High/Medium/Low",
        "verification_importance": "High/Medium/Low",
        "key_differentiators": []
    },
    "extraction_notes": {
        "data_extraction_confidence": "High/Medium/Low",
        "unclear_sections": [],
        "assumptions_made": [],
        "additional_verification_needed": []
    },
    "ai_analysis_summary": {
        "document_strength": "",
        "notable_features": [],
        "potential_concerns": [],
        "recommended_follow_up": [],
        "overall_assessment": ""
    }
}

ANALYSIS INSTRUCTIONS:
1. Read ALL text content thoroughly in this ${totalChunks > 1 ? 'chunk/section' : 'document'}
2. Analyze images, tables, charts, and visual elements
3. Extract specific details, not general descriptions
4. Identify document type and purpose accurately
5. Capture all relevant dates, names, organizations
6. Assess document quality and completeness ${totalChunks > 1 ? 'for this section' : ''}
7. Provide confidence levels for extracted information
8. Note any inconsistencies or red flags
9. ${totalChunks > 1 ? 'If information appears incomplete (cut off at page boundaries), note this in extraction_notes' : 'Create a profile that helps other AI systems quickly understand this document\'s value and content'}
10. ${totalChunks > 1 ? 'Focus on information that is complete within this chunk' : 'Be thorough and precise in extraction'}

Extract ALL available information ${totalChunks > 1 ? 'from this chunk' : ''}. If a field is not present, use null or empty array. Be thorough and precise.
`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [
                    {
                        text: prompt
                    },
                    {
                        inline_data: {
                            mime_type: "application/pdf",
                            data: pdfBase64
                        }
                    }
                ]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.95,
                maxOutputTokens: 8192,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts[0]) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}

// Display File Profile Results
function displayFileProfileResults(profileResult, fileName) {
    if (profileResult.error) {
        Swal.fire({
            title: 'Analysis Completed with Issues',
            html: `
                <div class="text-start">
                    <div class="alert alert-warning">
                        <strong>Error:</strong> ${profileResult.error}
                    </div>
                    <details>
                        <summary>Raw AI Response</summary>
                        <pre class="mt-2 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.9em;">${profileResult.raw_response || 'No response available'}</pre>
                    </details>
                </div>
            `,
            width: '90%',
            confirmButtonText: 'Close',
            confirmButtonColor: '#F00F00'
        });
        return;
    }

    // Build comprehensive results HTML
    let resultsHTML = `
        <div class="text-start">
            <div class="row mb-3">
                <div class="col-12">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-file-alt me-2"></i>File Profile: ${fileName}
                    </h5>
                </div>
            </div>
    `;

    // File Metadata Summary
    if (profileResult.file_metadata) {
        const metadata = profileResult.file_metadata;
        const isChunkedAnalysis = metadata.chunk_info || profileResult.chunk_analyses;

        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Document Overview</h6>
                    ${isChunkedAnalysis ? `
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Large Document Analysis:</strong>
                            ${metadata.total_pages || profileResult.file_metadata?.total_pages || 'Multiple'} pages analyzed in
                            ${metadata.total_chunks || profileResult.chunk_analyses?.length || 'multiple'} chunks for comprehensive coverage.
                        </div>
                    ` : ''}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-primary">${metadata.document_type || 'Unknown'}</div>
                                <small class="text-muted">Document Type</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-info">${metadata.document_category || 'Unknown'}</div>
                                <small class="text-muted">Category</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-success">${metadata.confidence_level || 'Unknown'}</div>
                                <small class="text-muted">Confidence</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-warning">${metadata.total_pages || profileResult.document_structure?.total_pages || 'N/A'}</div>
                                <small class="text-muted">Total Pages</small>
                            </div>
                        </div>
                    </div>
                    ${metadata.primary_purpose ? `<p class="mt-2 mb-0"><strong>Purpose:</strong> ${metadata.primary_purpose}</p>` : ''}
                </div>
            </div>
        `;
    }

    // Chunked Analysis Summary (if applicable)
    if (profileResult.chunk_analyses) {
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Chunk Analysis Summary</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-success text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.filter(chunk => chunk.analysis && !chunk.error).length}</div>
                                <small>Successful Analyses</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-warning text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.filter(chunk => chunk.error).length}</div>
                                <small>Failed Analyses</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-info text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.length}</div>
                                <small>Total Chunks</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <details>
                            <summary class="btn btn-outline-primary btn-sm">View Individual Chunk Results</summary>
                            <div class="mt-3">
                                ${profileResult.chunk_analyses.map((chunk, index) => `
                                    <div class="card mb-2">
                                        <div class="card-header py-2">
                                            <h6 class="mb-0">
                                                Chunk ${chunk.chunk_number} - Pages ${chunk.pages_range}
                                                ${chunk.error ? '<span class="badge bg-danger ms-2">Error</span>' : '<span class="badge bg-success ms-2">Success</span>'}
                                            </h6>
                                        </div>
                                        ${chunk.error ? `
                                            <div class="card-body py-2">
                                                <small class="text-danger">Error: ${chunk.error}</small>
                                            </div>
                                        ` : `
                                            <div class="card-body py-2">
                                                <small class="text-muted">
                                                    Document Type: ${chunk.analysis?.file_metadata?.document_type || 'Unknown'} |
                                                    Confidence: ${chunk.analysis?.file_metadata?.confidence_level || 'Unknown'}
                                                </small>
                                            </div>
                                        `}
                                    </div>
                                `).join('')}
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        `;
    }

    // Personal Information (use combined insights for chunked analysis)
    const personalInfo = profileResult.combined_insights?.personal_information || profileResult.personal_information;
    if (personalInfo) {
        const personal = personalInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Personal Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            ${personal.full_name ? `<p><strong>Name:</strong> ${personal.full_name}</p>` : ''}
                            ${personal.professional_title ? `<p><strong>Title:</strong> ${personal.professional_title}</p>` : ''}
                            ${personal.email_address ? `<p><strong>Email:</strong> ${personal.email_address}</p>` : ''}
                            ${personal.phone_number ? `<p><strong>Phone:</strong> ${personal.phone_number}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            ${personal.current_position ? `<p><strong>Position:</strong> ${personal.current_position}</p>` : ''}
                            ${personal.current_employer ? `<p><strong>Employer:</strong> ${personal.current_employer}</p>` : ''}
                            ${personal.nationality ? `<p><strong>Nationality:</strong> ${personal.nationality}</p>` : ''}
                            ${personal.linkedin_profile ? `<p><strong>LinkedIn:</strong> <a href="${personal.linkedin_profile}" target="_blank">${personal.linkedin_profile}</a></p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Professional Profile (use combined insights for chunked analysis)
    const professionalInfo = profileResult.combined_insights?.professional_profile || profileResult.professional_profile;
    if (professionalInfo) {
        const prof = professionalInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Professional Profile</h6>
                    <div class="row">
                        <div class="col-md-6">
                            ${prof.career_level ? `<p><strong>Career Level:</strong> <span class="badge bg-primary">${prof.career_level}</span></p>` : ''}
                            ${prof.years_of_experience ? `<p><strong>Experience:</strong> ${prof.years_of_experience}</p>` : ''}
                            ${prof.industry_focus && prof.industry_focus.length > 0 ? `<p><strong>Industry Focus:</strong> ${prof.industry_focus.join(', ')}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            ${prof.specializations && prof.specializations.length > 0 ? `<p><strong>Specializations:</strong> ${prof.specializations.join(', ')}</p>` : ''}
                            ${prof.current_role_summary ? `<p><strong>Current Role:</strong> ${prof.current_role_summary}</p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Skills & Competencies (use combined insights for chunked analysis)
    const skillsInfo = profileResult.combined_insights?.skills_competencies || profileResult.skills_competencies;
    if (skillsInfo) {
        const skills = skillsInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Skills & Competencies</h6>
                    <div class="row">
        `;

        if (skills.technical_skills && skills.technical_skills.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <strong>Technical Skills:</strong>
                    <div class="mt-1">
                        ${skills.technical_skills.map(skill => `<span class="badge bg-info me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        if (skills.soft_skills && skills.soft_skills.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <strong>Soft Skills:</strong>
                    <div class="mt-1">
                        ${skills.soft_skills.map(skill => `<span class="badge bg-success me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Document Quality Assessment (use combined insights for chunked analysis)
    const qualityInfo = profileResult.combined_insights?.document_quality_assessment || profileResult.document_quality_assessment;
    if (qualityInfo) {
        const quality = qualityInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Document Quality Assessment</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-primary">${quality.completeness_score || 'N/A'}</div>
                                <small class="text-muted">Completeness</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-info">${quality.presentation_quality || 'N/A'}</div>
                                <small class="text-muted">Presentation</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-success">${quality.information_clarity || 'N/A'}</div>
                                <small class="text-muted">Clarity</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 ${quality.professional_formatting ? 'text-success' : 'text-warning'}">${quality.professional_formatting ? 'Yes' : 'No'}</div>
                                <small class="text-muted">Professional Format</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // AI Analysis Summary
    if (profileResult.ai_analysis_summary) {
        const summary = profileResult.ai_analysis_summary;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">AI Analysis Summary</h6>
                    ${summary.overall_assessment ? `<p><strong>Overall Assessment:</strong> ${summary.overall_assessment}</p>` : ''}
                    ${summary.document_strength ? `<p><strong>Document Strength:</strong> ${summary.document_strength}</p>` : ''}

                    ${summary.notable_features && summary.notable_features.length > 0 ? `
                        <div class="mb-2">
                            <strong>Notable Features:</strong>
                            <ul class="mt-1">
                                ${summary.notable_features.map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${summary.recommended_follow_up && summary.recommended_follow_up.length > 0 ? `
                        <div class="mb-2">
                            <strong>Recommended Follow-up:</strong>
                            <ul class="mt-1">
                                ${summary.recommended_follow_up.map(item => `<li>${item}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Raw JSON Data (collapsible)
    resultsHTML += `
        <div class="row">
            <div class="col-12">
                <details>
                    <summary class="btn btn-outline-secondary btn-sm">View Complete JSON Profile</summary>
                    <pre class="mt-3 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.85em; max-height: 400px; overflow-y: auto;">${JSON.stringify(profileResult, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;

    resultsHTML += `</div>`;

    // Show results in modal
    Swal.fire({
        title: 'File Profile Analysis Complete',
        html: resultsHTML,
        width: '95%',
        confirmButtonText: 'Close',
        confirmButtonColor: '#F00F00',
        customClass: {
            popup: 'text-start'
        }
    });
}
</script>
<?= $this->endSection() ?>
